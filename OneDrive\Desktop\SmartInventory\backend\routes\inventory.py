"""
FastAPI routes for Smart Inventory Management System

This module defines all API endpoints for inventory management.
Designed for small Indian businesses with simple, intuitive operations.
"""

from typing import List, Optional
from fastapi import APIRouter, HTTPException, Query, Form, UploadFile, File
from fastapi.responses import JSONResponse, Response
from fastapi.responses import StreamingResponse
import io

from models.item import (
    Item,
    ItemCreate,
    ItemUpdate,
    ItemResponse,
    ItemListResponse
)
from crud import inventory_crud
from utils.csv_utils import export_inventory_to_csv, import_items_from_csv, get_csv_template

# Create router instance
router = APIRouter(prefix="/inventory", tags=["inventory"])


@router.post("/", response_model=ItemResponse, status_code=201)
async def add_item(item: ItemCreate):
    """
    Add a new item to inventory
    
    Args:
        item: Item data to create
        
    Returns:
        Created item with success message
    """
    try:
        new_item = inventory_crud.create_item(item)
        return ItemResponse(
            success=True,
            message=f"Item '{new_item.name}' added successfully to inventory",
            data=new_item
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Failed to add item: {str(e)}")


@router.get("/", response_model=ItemListResponse)
async def get_all_items(search: Optional[str] = Query(None, description="Search items by name")):
    """
    Get all inventory items or search by name
    
    Args:
        search: Optional search query to filter items by name
        
    Returns:
        List of all items with inventory statistics
    """
    try:
        if search:
            items = inventory_crud.search_items(search)
            message = f"Found {len(items)} items matching '{search}'"
        else:
            items = inventory_crud.get_all_items()
            message = f"Retrieved all {len(items)} items from inventory"
        
        stats = inventory_crud.get_inventory_stats()
        
        return ItemListResponse(
            success=True,
            message=message,
            data=items,
            total_items=stats["total_items"],
            total_value=stats["total_value"]
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve items: {str(e)}")


@router.get("/{item_id}", response_model=ItemResponse)
async def get_item(item_id: int):
    """
    Get a specific item by ID
    
    Args:
        item_id: ID of the item to retrieve
        
    Returns:
        Item data if found
    """
    item = inventory_crud.get_item_by_id(item_id)
    if not item:
        raise HTTPException(status_code=404, detail=f"Item with ID {item_id} not found")
    
    return ItemResponse(
        success=True,
        message=f"Item '{item.name}' retrieved successfully",
        data=item
    )


@router.put("/{item_id}", response_model=ItemResponse)
async def update_item(item_id: int, item_update: ItemUpdate):
    """
    Update an existing inventory item
    
    Args:
        item_id: ID of the item to update
        item_update: Updated item data
        
    Returns:
        Updated item data
    """
    # Check if item exists
    existing_item = inventory_crud.get_item_by_id(item_id)
    if not existing_item:
        raise HTTPException(status_code=404, detail=f"Item with ID {item_id} not found")
    
    try:
        updated_item = inventory_crud.update_item(item_id, item_update)
        if not updated_item:
            raise HTTPException(status_code=400, detail="Failed to update item")
        
        return ItemResponse(
            success=True,
            message=f"Item '{updated_item.name}' updated successfully",
            data=updated_item
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Failed to update item: {str(e)}")


@router.delete("/{item_id}", response_model=dict)
async def delete_item(item_id: int):
    """
    Delete an inventory item
    
    Args:
        item_id: ID of the item to delete
        
    Returns:
        Success message
    """
    # Check if item exists first
    existing_item = inventory_crud.get_item_by_id(item_id)
    if not existing_item:
        raise HTTPException(status_code=404, detail=f"Item with ID {item_id} not found")
    
    try:
        success = inventory_crud.delete_item(item_id)
        if not success:
            raise HTTPException(status_code=400, detail="Failed to delete item")
        
        return {
            "success": True,
            "message": f"Item '{existing_item.name}' deleted successfully from inventory"
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Failed to delete item: {str(e)}")


@router.get("/stats/summary")
async def get_inventory_summary():
    """
    Get inventory statistics and summary
    
    Returns:
        Inventory statistics including total items, value, and low stock alerts
    """
    try:
        stats = inventory_crud.get_inventory_stats()
        return {
            "success": True,
            "message": "Inventory summary retrieved successfully",
            "data": stats
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get inventory summary: {str(e)}")


@router.get("/alerts/low-stock")
async def get_low_stock_alerts(
    threshold: int = Query(default=10, ge=1, le=100, description="Low stock threshold")
):
    """
    Get items with low stock below specified threshold

    Args:
        threshold: Minimum quantity threshold for low stock alert

    Returns:
        List of items that need restocking
    """
    try:
        stats = inventory_crud.get_inventory_stats(low_stock_threshold=threshold)
        low_stock_items = stats["low_stock_items"]

        return {
            "success": True,
            "message": f"Found {len(low_stock_items)} items with low stock (threshold: {threshold})",
            "data": low_stock_items,
            "alert_count": len(low_stock_items),
            "threshold": threshold
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get low stock alerts: {str(e)}")


@router.get("/export/csv")
async def export_inventory_csv():
    """
    Export inventory to CSV format

    Returns:
        CSV file with all inventory items
    """
    try:
        csv_content = export_inventory_to_csv()

        # Create response with CSV content
        return Response(
            content=csv_content,
            media_type="text/csv",
            headers={"Content-Disposition": "attachment; filename=inventory_export.csv"}
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to export CSV: {str(e)}")


@router.post("/import/csv")
async def import_inventory_csv(
    file: UploadFile = File(...),
    replace_existing: bool = Form(default=False)
):
    """
    Import inventory from CSV file

    Args:
        file: CSV file to import
        replace_existing: Whether to replace existing inventory

    Returns:
        Import results and statistics
    """
    try:
        # Validate file type
        if not file.filename.endswith('.csv'):
            raise HTTPException(status_code=400, detail="File must be a CSV file")

        # Read file content
        content = await file.read()
        csv_content = content.decode('utf-8')

        # Import items
        result = import_items_from_csv(csv_content, replace_existing)

        return {
            "success": result["success"],
            "message": result["message"],
            "data": result
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to import CSV: {str(e)}")


@router.get("/template/csv")
async def download_csv_template():
    """
    Get CSV template for importing inventory

    Returns:
        CSV template file with sample data
    """
    try:
        csv_content = get_csv_template()

        return Response(
            content=csv_content,
            media_type="text/csv",
            headers={"Content-Disposition": "attachment; filename=inventory_template.csv"}
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to generate CSV template: {str(e)}")
