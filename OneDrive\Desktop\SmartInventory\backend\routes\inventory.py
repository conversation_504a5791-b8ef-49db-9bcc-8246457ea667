"""
FastAPI routes for Smart Inventory Management System

This module defines all API endpoints for inventory management.
Designed for small Indian businesses with simple, intuitive operations.
"""

from typing import List, Optional
from fastapi import APIRouter, HTTPException, Query
from fastapi.responses import JSONResponse

from models.item import (
    Item,
    ItemCreate,
    ItemUpdate,
    ItemResponse,
    ItemListResponse
)
from crud import inventory_crud

# Create router instance
router = APIRouter(prefix="/inventory", tags=["inventory"])


@router.post("/", response_model=ItemResponse, status_code=201)
async def add_item(item: ItemCreate):
    """
    Add a new item to inventory
    
    Args:
        item: Item data to create
        
    Returns:
        Created item with success message
    """
    try:
        new_item = inventory_crud.create_item(item)
        return ItemResponse(
            success=True,
            message=f"Item '{new_item.name}' added successfully to inventory",
            data=new_item
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Failed to add item: {str(e)}")


@router.get("/", response_model=ItemListResponse)
async def get_all_items(search: Optional[str] = Query(None, description="Search items by name")):
    """
    Get all inventory items or search by name
    
    Args:
        search: Optional search query to filter items by name
        
    Returns:
        List of all items with inventory statistics
    """
    try:
        if search:
            items = inventory_crud.search_items(search)
            message = f"Found {len(items)} items matching '{search}'"
        else:
            items = inventory_crud.get_all_items()
            message = f"Retrieved all {len(items)} items from inventory"
        
        stats = inventory_crud.get_inventory_stats()
        
        return ItemListResponse(
            success=True,
            message=message,
            data=items,
            total_items=stats["total_items"],
            total_value=stats["total_value"]
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve items: {str(e)}")


@router.get("/{item_id}", response_model=ItemResponse)
async def get_item(item_id: int):
    """
    Get a specific item by ID
    
    Args:
        item_id: ID of the item to retrieve
        
    Returns:
        Item data if found
    """
    item = inventory_crud.get_item_by_id(item_id)
    if not item:
        raise HTTPException(status_code=404, detail=f"Item with ID {item_id} not found")
    
    return ItemResponse(
        success=True,
        message=f"Item '{item.name}' retrieved successfully",
        data=item
    )


@router.put("/{item_id}", response_model=ItemResponse)
async def update_item(item_id: int, item_update: ItemUpdate):
    """
    Update an existing inventory item
    
    Args:
        item_id: ID of the item to update
        item_update: Updated item data
        
    Returns:
        Updated item data
    """
    # Check if item exists
    existing_item = inventory_crud.get_item_by_id(item_id)
    if not existing_item:
        raise HTTPException(status_code=404, detail=f"Item with ID {item_id} not found")
    
    try:
        updated_item = inventory_crud.update_item(item_id, item_update)
        if not updated_item:
            raise HTTPException(status_code=400, detail="Failed to update item")
        
        return ItemResponse(
            success=True,
            message=f"Item '{updated_item.name}' updated successfully",
            data=updated_item
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Failed to update item: {str(e)}")


@router.delete("/{item_id}", response_model=dict)
async def delete_item(item_id: int):
    """
    Delete an inventory item
    
    Args:
        item_id: ID of the item to delete
        
    Returns:
        Success message
    """
    # Check if item exists first
    existing_item = inventory_crud.get_item_by_id(item_id)
    if not existing_item:
        raise HTTPException(status_code=404, detail=f"Item with ID {item_id} not found")
    
    try:
        success = inventory_crud.delete_item(item_id)
        if not success:
            raise HTTPException(status_code=400, detail="Failed to delete item")
        
        return {
            "success": True,
            "message": f"Item '{existing_item.name}' deleted successfully from inventory"
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Failed to delete item: {str(e)}")


@router.get("/stats/summary")
async def get_inventory_summary():
    """
    Get inventory statistics and summary
    
    Returns:
        Inventory statistics including total items, value, and low stock alerts
    """
    try:
        stats = inventory_crud.get_inventory_stats()
        return {
            "success": True,
            "message": "Inventory summary retrieved successfully",
            "data": stats
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get inventory summary: {str(e)}")


@router.get("/alerts/low-stock")
async def get_low_stock_alerts():
    """
    Get items with low stock (quantity < 10)
    
    Returns:
        List of items that need restocking
    """
    try:
        stats = inventory_crud.get_inventory_stats()
        low_stock_items = stats["low_stock_items"]
        
        return {
            "success": True,
            "message": f"Found {len(low_stock_items)} items with low stock",
            "data": low_stock_items,
            "alert_count": len(low_stock_items)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get low stock alerts: {str(e)}")
