"""
Smart Inventory Management System - Main FastAPI Application

A simple, efficient inventory management system designed for small Indian businesses.
Features include item management, stock tracking, and low stock alerts.

Author: Smart Inventory Team
Version: 1.0.0
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
import os
from pathlib import Path

from routes import inventory

# Create FastAPI application instance
app = FastAPI(
    title="Smart Inventory Management System",
    description="""
    A comprehensive inventory management system designed specifically for small Indian businesses.
    
    ## Features
    * **Add Items**: Easily add new products to your inventory
    * **Update Stock**: Modify item details and quantities
    * **Track Inventory**: View all items with real-time stock levels
    * **Delete Items**: Remove discontinued products
    * **Search**: Find items quickly by name
    * **Low Stock Alerts**: Get notified when items need restocking
    * **Inventory Stats**: View total value and item counts
    
    ## Perfect For
    * Small grocery stores
    * Medical shops
    * General stores
    * Retail businesses
    * Wholesale distributors
    
    Built with ❤️ for Indian small business owners.
    """,
    version="1.0.0",
    contact={
        "name": "Smart Inventory Support",
        "email": "<EMAIL>",
    },
    license_info={
        "name": "MIT License",
        "url": "https://opensource.org/licenses/MIT",
    },
)

# Configure CORS to allow frontend access
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",  # React development server
        "http://localhost:8080",  # Alternative frontend port
        "http://127.0.0.1:3000",
        "http://127.0.0.1:8080",
        "http://localhost:5500",  # Live Server extension
        "http://127.0.0.1:5500",
        "file://",  # Allow file:// protocol for local HTML files
        "*"  # Allow all origins for development (remove in production)
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# Include inventory routes
app.include_router(inventory.router, prefix="/api")

# Serve static files (frontend) if available
frontend_path = Path(__file__).parent.parent / "frontend"
if frontend_path.exists():
    app.mount("/static", StaticFiles(directory=str(frontend_path)), name="static")


@app.get("/", tags=["root"])
async def root():
    """
    Root endpoint with API information
    """
    return {
        "message": "Welcome to Smart Inventory Management System",
        "description": "A simple inventory management solution for small Indian businesses",
        "version": "1.0.0",
        "docs_url": "/docs",
        "api_prefix": "/api",
        "features": [
            "Add, update, delete inventory items",
            "Real-time stock tracking",
            "Search functionality",
            "Low stock alerts",
            "Inventory statistics",
            "CORS enabled for frontend integration"
        ],
        "sample_endpoints": {
            "get_all_items": "/api/inventory/",
            "add_item": "POST /api/inventory/",
            "update_item": "PUT /api/inventory/{item_id}",
            "delete_item": "DELETE /api/inventory/{item_id}",
            "search_items": "/api/inventory/?search=rice",
            "low_stock_alerts": "/api/inventory/alerts/low-stock",
            "inventory_stats": "/api/inventory/stats/summary"
        }
    }


@app.get("/health", tags=["health"])
async def health_check():
    """
    Health check endpoint
    """
    return {
        "status": "healthy",
        "message": "Smart Inventory Management System is running",
        "version": "1.0.0"
    }


@app.exception_handler(404)
async def not_found_handler(request, exc):
    """
    Custom 404 handler
    """
    return JSONResponse(
        status_code=404,
        content={
            "success": False,
            "message": "Endpoint not found",
            "detail": "The requested resource was not found on this server",
            "available_endpoints": {
                "api_docs": "/docs",
                "inventory": "/api/inventory/",
                "health": "/health"
            }
        }
    )


@app.exception_handler(500)
async def internal_error_handler(request, exc):
    """
    Custom 500 handler
    """
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "message": "Internal server error",
            "detail": "An unexpected error occurred. Please try again later."
        }
    )


# Startup event
@app.on_event("startup")
async def startup_event():
    """
    Application startup event
    """
    print("🚀 Smart Inventory Management System starting up...")
    print("📦 Loading sample inventory data...")
    print("✅ System ready for small business inventory management!")


# Shutdown event
@app.on_event("shutdown")
async def shutdown_event():
    """
    Application shutdown event
    """
    print("🛑 Smart Inventory Management System shutting down...")
    print("💾 Saving inventory data...")
    print("👋 Goodbye!")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
