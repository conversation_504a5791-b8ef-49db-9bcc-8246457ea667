/**
 * Smart Inventory Management System - Frontend JavaScript
 * Designed for Small Indian Businesses
 * 
 * This file handles all frontend interactions with the FastAPI backend
 * Features: CRUD operations, real-time updates, search, and notifications
 */

// Configuration
const API_BASE_URL = 'http://127.0.0.1:8000/api';
let currentEditingId = null;
let allItems = [];

// DOM Elements
const elements = {
    // Stats
    totalItems: document.getElementById('totalItems'),
    totalValue: document.getElementById('totalValue'),
    lowStockCount: document.getElementById('lowStockCount'),
    lowStockCard: document.getElementById('lowStockCard'),
    
    // Search and controls
    searchInput: document.getElementById('searchInput'),
    clearSearch: document.getElementById('clearSearch'),
    addItemBtn: document.getElementById('addItemBtn'),
    refreshBtn: document.getElementById('refreshBtn'),
    lowStockBtn: document.getElementById('lowStockBtn'),
    
    // Form
    formSection: document.getElementById('formSection'),
    formTitle: document.getElementById('formTitle'),
    itemForm: document.getElementById('itemForm'),
    closeFormBtn: document.getElementById('closeFormBtn'),
    cancelBtn: document.getElementById('cancelBtn'),
    submitBtn: document.getElementById('submitBtn'),
    
    // Form inputs
    itemName: document.getElementById('itemName'),
    itemQuantity: document.getElementById('itemQuantity'),
    itemPrice: document.getElementById('itemPrice'),
    
    // Table
    inventoryTableBody: document.getElementById('inventoryTableBody'),
    loadingState: document.getElementById('loadingState'),
    emptyState: document.getElementById('emptyState'),
    
    // Toast and modal
    toast: document.getElementById('toast'),
    confirmModal: document.getElementById('confirmModal'),
    confirmMessage: document.getElementById('confirmMessage'),
    confirmOk: document.getElementById('confirmOk'),
    confirmCancel: document.getElementById('confirmCancel')
};

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    setupEventListeners();
    loadInventory();
});

/**
 * Initialize the application
 */
function initializeApp() {
    console.log('🚀 Smart Inventory Management System initialized');
    console.log('📦 Perfect for small Indian businesses!');
    
    // Hide loading state initially
    hideLoading();
}

/**
 * Setup all event listeners
 */
function setupEventListeners() {
    // Search functionality
    elements.searchInput.addEventListener('input', handleSearch);
    elements.clearSearch.addEventListener('click', clearSearch);
    
    // Control buttons
    elements.addItemBtn.addEventListener('click', showAddForm);
    elements.refreshBtn.addEventListener('click', loadInventory);
    elements.lowStockBtn.addEventListener('click', showLowStockItems);
    
    // Form handling
    elements.closeFormBtn.addEventListener('click', hideForm);
    elements.cancelBtn.addEventListener('click', hideForm);
    elements.itemForm.addEventListener('submit', handleFormSubmit);
    
    // Modal handling
    elements.confirmCancel.addEventListener('click', hideConfirmModal);
    
    // Keyboard shortcuts
    document.addEventListener('keydown', handleKeyboardShortcuts);
    
    // Click outside form to close
    elements.formSection.addEventListener('click', function(e) {
        if (e.target === elements.formSection) {
            hideForm();
        }
    });
}

/**
 * Load inventory data from API
 */
async function loadInventory() {
    try {
        showLoading();
        
        const response = await fetch(`${API_BASE_URL}/inventory/`);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data.success) {
            allItems = data.data;
            displayItems(allItems);
            updateStats(data.total_items, data.total_value);
            await updateLowStockAlert();
        } else {
            throw new Error(data.message || 'Failed to load inventory');
        }
        
    } catch (error) {
        console.error('Error loading inventory:', error);
        showToast('Failed to load inventory. Please check if the server is running.', 'error');
        showEmptyState();
    } finally {
        hideLoading();
    }
}

/**
 * Display items in the table
 */
function displayItems(items) {
    const tbody = elements.inventoryTableBody;
    
    if (items.length === 0) {
        showEmptyState();
        return;
    }
    
    hideEmptyState();
    
    tbody.innerHTML = items.map(item => `
        <tr data-id="${item.id}">
            <td>${item.id}</td>
            <td class="item-name">${escapeHtml(item.name)}</td>
            <td class="quantity">${item.quantity}</td>
            <td class="price">₹${item.price.toFixed(2)}</td>
            <td class="total-value">₹${(item.quantity * item.price).toFixed(2)}</td>
            <td>
                <span class="status-badge ${getStatusClass(item.quantity)}">
                    ${getStatusText(item.quantity)}
                </span>
            </td>
            <td class="action-buttons-cell">
                <button class="btn btn-sm btn-secondary" onclick="editItem(${item.id})">
                    <i class="fas fa-edit"></i> Edit
                </button>
                <button class="btn btn-sm btn-danger" onclick="confirmDeleteItem(${item.id}, '${escapeHtml(item.name)}')">
                    <i class="fas fa-trash"></i> Delete
                </button>
            </td>
        </tr>
    `).join('');
}

/**
 * Update statistics display
 */
function updateStats(totalItems, totalValue) {
    elements.totalItems.textContent = totalItems;
    elements.totalValue.textContent = `₹${totalValue.toFixed(2)}`;
}

/**
 * Update low stock alert
 */
async function updateLowStockAlert() {
    try {
        const response = await fetch(`${API_BASE_URL}/inventory/alerts/low-stock`);
        if (response.ok) {
            const data = await response.json();
            const lowStockCount = data.alert_count || 0;
            
            elements.lowStockCount.textContent = lowStockCount;
            
            if (lowStockCount > 0) {
                elements.lowStockCard.classList.add('alert');
                elements.lowStockBtn.style.display = 'inline-flex';
            } else {
                elements.lowStockCard.classList.remove('alert');
                elements.lowStockBtn.style.display = 'none';
            }
        }
    } catch (error) {
        console.error('Error updating low stock alert:', error);
    }
}

/**
 * Handle search functionality
 */
function handleSearch() {
    const query = elements.searchInput.value.toLowerCase().trim();
    
    if (query === '') {
        displayItems(allItems);
        elements.clearSearch.style.display = 'none';
    } else {
        const filteredItems = allItems.filter(item => 
            item.name.toLowerCase().includes(query)
        );
        displayItems(filteredItems);
        elements.clearSearch.style.display = 'block';
    }
}

/**
 * Clear search
 */
function clearSearch() {
    elements.searchInput.value = '';
    elements.clearSearch.style.display = 'none';
    displayItems(allItems);
}

/**
 * Show add item form
 */
function showAddForm() {
    currentEditingId = null;
    elements.formTitle.textContent = 'Add New Item';
    elements.submitBtn.innerHTML = '<i class="fas fa-save"></i> Save Item';
    elements.itemForm.reset();
    elements.formSection.style.display = 'block';
    elements.itemName.focus();
}

/**
 * Show edit item form
 */
function editItem(id) {
    const item = allItems.find(item => item.id === id);
    if (!item) {
        showToast('Item not found', 'error');
        return;
    }
    
    currentEditingId = id;
    elements.formTitle.textContent = 'Edit Item';
    elements.submitBtn.innerHTML = '<i class="fas fa-save"></i> Update Item';
    
    elements.itemName.value = item.name;
    elements.itemQuantity.value = item.quantity;
    elements.itemPrice.value = item.price;
    
    elements.formSection.style.display = 'block';
    elements.itemName.focus();
}

/**
 * Hide form
 */
function hideForm() {
    elements.formSection.style.display = 'none';
    elements.itemForm.reset();
    currentEditingId = null;
}

/**
 * Handle form submission
 */
async function handleFormSubmit(e) {
    e.preventDefault();
    
    const formData = new FormData(elements.itemForm);
    const itemData = {
        name: formData.get('name').trim(),
        quantity: parseInt(formData.get('quantity')),
        price: parseFloat(formData.get('price'))
    };
    
    // Validation
    if (!itemData.name) {
        showToast('Please enter item name', 'error');
        return;
    }
    
    if (itemData.quantity < 0) {
        showToast('Quantity cannot be negative', 'error');
        return;
    }
    
    if (itemData.price <= 0) {
        showToast('Price must be greater than 0', 'error');
        return;
    }
    
    try {
        elements.submitBtn.disabled = true;
        elements.submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
        
        let response;
        if (currentEditingId) {
            // Update existing item
            response = await fetch(`${API_BASE_URL}/inventory/${currentEditingId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(itemData)
            });
        } else {
            // Create new item
            response = await fetch(`${API_BASE_URL}/inventory/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(itemData)
            });
        }
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const result = await response.json();
        
        if (result.success) {
            showToast(result.message, 'success');
            hideForm();
            await loadInventory();
        } else {
            throw new Error(result.message || 'Operation failed');
        }
        
    } catch (error) {
        console.error('Error saving item:', error);
        showToast('Failed to save item. Please try again.', 'error');
    } finally {
        elements.submitBtn.disabled = false;
        elements.submitBtn.innerHTML = currentEditingId ?
            '<i class="fas fa-save"></i> Update Item' :
            '<i class="fas fa-save"></i> Save Item';
    }
}

/**
 * Confirm delete item
 */
function confirmDeleteItem(id, name) {
    elements.confirmMessage.textContent = `Are you sure you want to delete "${name}"? This action cannot be undone.`;
    elements.confirmModal.classList.add('show');

    elements.confirmOk.onclick = () => {
        hideConfirmModal();
        deleteItem(id);
    };
}

/**
 * Delete item
 */
async function deleteItem(id) {
    try {
        const response = await fetch(`${API_BASE_URL}/inventory/${id}`, {
            method: 'DELETE'
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();

        if (result.success) {
            showToast(result.message, 'success');
            await loadInventory();
        } else {
            throw new Error(result.message || 'Delete failed');
        }

    } catch (error) {
        console.error('Error deleting item:', error);
        showToast('Failed to delete item. Please try again.', 'error');
    }
}

/**
 * Show low stock items
 */
async function showLowStockItems() {
    try {
        const response = await fetch(`${API_BASE_URL}/inventory/alerts/low-stock`);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (data.success) {
            displayItems(data.data);
            showToast(`Showing ${data.data.length} items with low stock`, 'warning');
        } else {
            throw new Error(data.message || 'Failed to load low stock items');
        }

    } catch (error) {
        console.error('Error loading low stock items:', error);
        showToast('Failed to load low stock items', 'error');
    }
}

/**
 * Handle keyboard shortcuts
 */
function handleKeyboardShortcuts(e) {
    // Ctrl/Cmd + N: Add new item
    if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
        e.preventDefault();
        showAddForm();
    }

    // Escape: Close form/modal
    if (e.key === 'Escape') {
        if (elements.formSection.style.display === 'block') {
            hideForm();
        }
        if (elements.confirmModal.classList.contains('show')) {
            hideConfirmModal();
        }
    }

    // Ctrl/Cmd + F: Focus search
    if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
        e.preventDefault();
        elements.searchInput.focus();
    }

    // F5: Refresh
    if (e.key === 'F5') {
        e.preventDefault();
        loadInventory();
    }
}

/**
 * Show loading state
 */
function showLoading() {
    elements.loadingState.style.display = 'block';
    elements.emptyState.style.display = 'none';
}

/**
 * Hide loading state
 */
function hideLoading() {
    elements.loadingState.style.display = 'none';
}

/**
 * Show empty state
 */
function showEmptyState() {
    elements.emptyState.style.display = 'block';
    elements.inventoryTableBody.innerHTML = '';
}

/**
 * Hide empty state
 */
function hideEmptyState() {
    elements.emptyState.style.display = 'none';
}

/**
 * Hide confirm modal
 */
function hideConfirmModal() {
    elements.confirmModal.classList.remove('show');
}

/**
 * Show toast notification
 */
function showToast(message, type = 'info') {
    const toast = elements.toast;
    const icon = toast.querySelector('.toast-icon');
    const messageEl = toast.querySelector('.toast-message');

    // Set icon based on type
    const icons = {
        success: 'fas fa-check-circle',
        error: 'fas fa-exclamation-circle',
        warning: 'fas fa-exclamation-triangle',
        info: 'fas fa-info-circle'
    };

    icon.className = `toast-icon ${icons[type] || icons.info}`;
    messageEl.textContent = message;

    // Remove existing type classes and add new one
    toast.className = `toast ${type}`;

    // Show toast
    toast.classList.add('show');

    // Auto hide after 5 seconds
    setTimeout(() => {
        toast.classList.remove('show');
    }, 5000);
}

/**
 * Get status class based on quantity
 */
function getStatusClass(quantity) {
    if (quantity === 0) return 'status-out-of-stock';
    if (quantity < 10) return 'status-low-stock';
    return 'status-in-stock';
}

/**
 * Get status text based on quantity
 */
function getStatusText(quantity) {
    if (quantity === 0) return 'Out of Stock';
    if (quantity < 10) return 'Low Stock';
    return 'In Stock';
}

/**
 * Escape HTML to prevent XSS
 */
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

/**
 * Format currency for Indian businesses
 */
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: 'INR'
    }).format(amount);
}

/**
 * Export inventory data (future enhancement)
 */
function exportInventory() {
    // This can be implemented later for CSV/Excel export
    showToast('Export feature coming soon!', 'info');
}

// Global functions for HTML onclick handlers
window.editItem = editItem;
window.confirmDeleteItem = confirmDeleteItem;
window.showAddForm = showAddForm;
