"""
Server runner for Smart Inventory Management System

This script starts the FastAPI server with optimal configuration for development.
Perfect for small Indian businesses to run their inventory system locally.
"""

import uvicorn
import os
import sys
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))


def run_server():
    """
    Start the FastAPI server with development configuration
    """
    print("[SHOP] Starting Smart Inventory Management System...")
    print("[BOX] Perfect for small Indian businesses!")
    print("=" * 50)

    # Server configuration
    config = {
        "app": "main:app",
        "host": "127.0.0.1",  # localhost only for security
        "port": 8000,
        "reload": True,  # Auto-reload on code changes
        "log_level": "info",
        "access_log": True,
        "reload_dirs": [str(backend_dir)],  # Watch backend directory for changes
    }

    print(f"[WEB] Server will start at: http://{config['host']}:{config['port']}")
    print(f"[DOCS] API Documentation: http://{config['host']}:{config['port']}/docs")
    print(f"[AUTO] Auto-reload: {'Enabled' if config['reload'] else 'Disabled'}")
    print("=" * 50)
    print("[TIP] Tips for small business owners:")
    print("   * Keep this terminal window open while using the system")
    print("   * Access the web interface through your browser")
    print("   * The system automatically saves your inventory data")
    print("   * Press Ctrl+C to stop the server when done")
    print("=" * 50)

    try:
        # Start the server
        uvicorn.run(**config)
    except KeyboardInterrupt:
        print("\n[STOP] Server stopped by user")
        print("[SAVE] Your inventory data has been saved")
        print("[BYE] Thank you for using Smart Inventory Management System!")
    except Exception as e:
        print(f"\n[ERROR] Error starting server: {e}")
        print("[FIX] Please check your Python environment and try again")
        sys.exit(1)


def check_dependencies():
    """
    Check if required dependencies are installed
    """
    required_packages = ["fastapi", "uvicorn", "pydantic"]
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("[ERROR] Missing required packages:")
        for package in missing_packages:
            print(f"   * {package}")
        print("\n[INSTALL] Install missing packages with:")
        print(f"   pip install {' '.join(missing_packages)}")
        print("\n[TIP] Or install all requirements with:")
        print("   pip install -r requirements.txt")
        return False
    
    return True


def main():
    """
    Main entry point
    """
    print("[ROCKET] Smart Inventory Management System")
    print("         Designed for Small Indian Businesses")
    print("         Version 1.0.0")
    print()

    # Check dependencies
    if not check_dependencies():
        sys.exit(1)

    # Check if we're in the right directory
    if not Path("main.py").exists():
        print("[ERROR] Error: main.py not found in current directory")
        print("[TIP] Please run this script from the backend directory")
        sys.exit(1)

    # Start the server
    run_server()


if __name__ == "__main__":
    main()
