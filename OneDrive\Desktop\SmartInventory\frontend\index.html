<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Inventory Management - Small Business Solution</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-store"></i>
                    <h1>Smart Inventory</h1>
                    <span class="tagline">For Small Indian Businesses</span>
                </div>
                <div class="header-stats">
                    <div class="stat-card">
                        <span class="stat-label">Total Items</span>
                        <span class="stat-value" id="totalItems">0</span>
                    </div>
                    <div class="stat-card">
                        <span class="stat-label">Total Value</span>
                        <span class="stat-value" id="totalValue">₹0</span>
                    </div>
                    <div class="stat-card alert" id="lowStockCard">
                        <span class="stat-label">Low Stock</span>
                        <span class="stat-value" id="lowStockCount">0</span>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <!-- Controls Section -->
            <section class="controls">
                <div class="search-section">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" id="searchInput" placeholder="Search items by name...">
                        <button type="button" id="clearSearch" class="clear-btn">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                
                <div class="action-buttons">
                    <button type="button" id="addItemBtn" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        Add New Item
                    </button>
                    <button type="button" id="refreshBtn" class="btn btn-secondary">
                        <i class="fas fa-refresh"></i>
                        Refresh
                    </button>
                </div>
            </section>

            <!-- Add/Edit Item Form -->
            <section class="form-section" id="formSection" style="display: none;">
                <div class="form-container">
                    <div class="form-header">
                        <h2 id="formTitle">Add New Item</h2>
                        <button type="button" id="closeFormBtn" class="close-btn">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    
                    <form id="itemForm">
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="itemName">Item Name *</label>
                                <input type="text" id="itemName" name="name" required 
                                       placeholder="e.g., Basmati Rice 1kg">
                            </div>
                            
                            <div class="form-group">
                                <label for="itemQuantity">Quantity *</label>
                                <input type="number" id="itemQuantity" name="quantity" required 
                                       min="0" placeholder="e.g., 50">
                            </div>
                            
                            <div class="form-group">
                                <label for="itemPrice">Price (₹) *</label>
                                <input type="number" id="itemPrice" name="price" required 
                                       min="0" step="0.01" placeholder="e.g., 120.00">
                            </div>
                        </div>
                        
                        <div class="form-actions">
                            <button type="button" id="cancelBtn" class="btn btn-secondary">Cancel</button>
                            <button type="submit" id="submitBtn" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                Save Item
                            </button>
                        </div>
                    </form>
                </div>
            </section>

            <!-- Inventory Table -->
            <section class="inventory-section">
                <div class="table-header">
                    <h2>Inventory Items</h2>
                    <div class="table-actions">
                        <button type="button" id="lowStockBtn" class="btn btn-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            Show Low Stock
                        </button>
                    </div>
                </div>
                
                <div class="table-container">
                    <table class="inventory-table" id="inventoryTable">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Item Name</th>
                                <th>Quantity</th>
                                <th>Price (₹)</th>
                                <th>Total Value (₹)</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="inventoryTableBody">
                            <!-- Items will be loaded here -->
                        </tbody>
                    </table>
                    
                    <!-- Loading State -->
                    <div class="loading" id="loadingState">
                        <i class="fas fa-spinner fa-spin"></i>
                        <span>Loading inventory...</span>
                    </div>
                    
                    <!-- Empty State -->
                    <div class="empty-state" id="emptyState" style="display: none;">
                        <i class="fas fa-box-open"></i>
                        <h3>No Items Found</h3>
                        <p>Start by adding your first inventory item</p>
                        <button type="button" class="btn btn-primary" onclick="showAddForm()">
                            <i class="fas fa-plus"></i>
                            Add First Item
                        </button>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 Smart Inventory Management System. Designed for Small Indian Businesses.</p>
            <p>Simple • Efficient • Reliable</p>
        </div>
    </footer>

    <!-- Notification Toast -->
    <div class="toast" id="toast">
        <div class="toast-content">
            <i class="toast-icon"></i>
            <span class="toast-message"></span>
        </div>
    </div>

    <!-- Confirmation Modal -->
    <div class="modal" id="confirmModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Confirm Action</h3>
            </div>
            <div class="modal-body">
                <p id="confirmMessage">Are you sure you want to perform this action?</p>
            </div>
            <div class="modal-actions">
                <button type="button" id="confirmCancel" class="btn btn-secondary">Cancel</button>
                <button type="button" id="confirmOk" class="btn btn-danger">Confirm</button>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>
