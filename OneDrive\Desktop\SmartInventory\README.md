# 🏪 Smart Inventory Management System

**A Simple, Efficient Inventory Management Solution for Small Indian Businesses**

Perfect for grocery stores, medical shops, general stores, and retail businesses that need an easy-to-use alternative to complex enterprise solutions like SAP or Zoho Inventory.

## 🌟 Features

### ✅ Core Functionality
- **Add Items**: Easily add new products to your inventory
- **Update Stock**: Modify item details and quantities in real-time
- **View Inventory**: See all items with current stock levels
- **Delete Items**: Remove discontinued products
- **Search**: Find items quickly by name
- **Real-time Updates**: Changes reflect immediately across the interface

### 📊 Smart Features
- **Low Stock Alerts**: Get notified when items need restocking (< 10 units)
- **Inventory Statistics**: View total items count and inventory value
- **Status Indicators**: Visual indicators for stock levels (In Stock, Low Stock, Out of Stock)
- **Responsive Design**: Works perfectly on desktop, tablet, and mobile devices

### 🚀 Business Benefits
- **No Complex Setup**: Start using immediately without complicated configuration
- **Cost-Effective**: Free alternative to expensive inventory management software
- **User-Friendly**: Designed specifically for small business owners
- **Local Storage**: Your data stays on your computer - no cloud dependency
- **Fast Performance**: Lightweight and responsive interface

## 📁 Project Structure

```
SmartInventory/
├── backend/                 # FastAPI Backend
│   ├── main.py             # Main FastAPI application
│   ├── run.py              # Server startup script
│   ├── requirements.txt    # Python dependencies
│   ├── models/
│   │   └── item.py         # Pydantic data models
│   ├── routes/
│   │   └── inventory.py    # API endpoints
│   └── crud/
│       └── inventory_crud.py # Database operations
├── frontend/               # HTML/CSS/JS Frontend
│   ├── index.html         # Main web interface
│   ├── style.css          # Styling and responsive design
│   └── app.js             # Frontend functionality
└── README.md              # This documentation
```

## 🛠️ Installation & Setup

### Prerequisites
- Python 3.8 or higher
- Modern web browser (Chrome, Firefox, Safari, Edge)

### Step 1: Install Python Dependencies

1. **Navigate to the backend directory:**
   ```bash
   cd backend
   ```

2. **Install required packages:**
   ```bash
   pip install -r requirements.txt
   ```

   Or install individually:
   ```bash
   pip install fastapi uvicorn pydantic
   ```

### Step 2: Start the Backend Server

1. **Run the server:**
   ```bash
   python run.py
   ```

   Or alternatively:
   ```bash
   python -m uvicorn main:app --reload --host 127.0.0.1 --port 8000
   ```

2. **Verify the server is running:**
   - Open your browser and go to: `http://127.0.0.1:8000`
   - You should see the API welcome message
   - API documentation is available at: `http://127.0.0.1:8000/docs`

### Step 3: Open the Frontend

1. **Navigate to the frontend directory:**
   ```bash
   cd ../frontend
   ```

2. **Open the web interface:**
   - **Option 1**: Double-click `index.html` to open in your browser
   - **Option 2**: Use a local server (recommended):
     ```bash
     # If you have Python installed
     python -m http.server 8080
     # Then open http://127.0.0.1:8080
     ```
   - **Option 3**: Use VS Code Live Server extension

## 📖 Usage Guide

### Adding New Items

1. Click the **"Add New Item"** button
2. Fill in the item details:
   - **Item Name**: e.g., "Basmati Rice 1kg"
   - **Quantity**: Current stock count
   - **Price**: Price per unit in ₹
3. Click **"Save Item"**

### Updating Items

1. Find the item in the inventory table
2. Click the **"Edit"** button
3. Modify the details as needed
4. Click **"Update Item"**

### Deleting Items

1. Find the item in the inventory table
2. Click the **"Delete"** button
3. Confirm the deletion in the popup

### Searching Items

1. Use the search box at the top
2. Type any part of the item name
3. Results filter automatically as you type

### Managing Low Stock

1. Items with quantity < 10 are marked as "Low Stock"
2. The header shows total low stock count
3. Click **"Show Low Stock"** to filter low stock items only

## 📊 Sample Data

The system comes pre-loaded with sample inventory items typical for Indian businesses:

| Item Name | Quantity | Price (₹) | Status |
|-----------|----------|-----------|---------|
| Basmati Rice 1kg | 50 | 120.00 | In Stock |
| Toor Dal 500g | 30 | 85.00 | In Stock |
| Sunflower Oil 1L | 25 | 140.00 | In Stock |
| Wheat Flour 1kg | 40 | 45.00 | In Stock |
| Sugar 1kg | 35 | 55.00 | In Stock |

## 🔧 API Endpoints

The backend provides a RESTful API with the following endpoints:

- `GET /api/inventory/` - Get all items
- `POST /api/inventory/` - Add new item
- `GET /api/inventory/{id}` - Get specific item
- `PUT /api/inventory/{id}` - Update item
- `DELETE /api/inventory/{id}` - Delete item
- `GET /api/inventory/stats/summary` - Get inventory statistics
- `GET /api/inventory/alerts/low-stock` - Get low stock alerts

## ⌨️ Keyboard Shortcuts

- **Ctrl/Cmd + N**: Add new item
- **Ctrl/Cmd + F**: Focus search box
- **F5**: Refresh inventory
- **Escape**: Close forms/modals

## 🔮 Future Enhancements (Planned)

### AI-Powered Features
- **Low Stock Predictions**: AI-based forecasting for restock timing
- **Demand Forecasting**: Predict future demand based on historical data
- **Auto Restock Suggestions**: Smart recommendations for reorder quantities
- **Sales Pattern Analysis**: Identify best-selling items and trends
- **Chatbot Interface**: Natural language queries like "Show me items running low"

### Additional Features
- **Barcode Scanning**: Quick item addition using mobile camera
- **Export/Import**: CSV and Excel support for data management
- **Multi-location Support**: Manage inventory across multiple stores
- **Supplier Management**: Track supplier information and purchase history
- **Sales Integration**: Connect with POS systems for automatic stock updates

## 🛡️ Security & Data

- **Local Storage**: All data is stored locally on your computer
- **No Cloud Dependency**: Works completely offline after setup
- **CORS Enabled**: Secure cross-origin requests between frontend and backend
- **Input Validation**: All user inputs are validated for security

## 🐛 Troubleshooting

### Common Issues

1. **"Failed to load inventory" error:**
   - Ensure the backend server is running on port 8000
   - Check if Python dependencies are installed correctly

2. **CORS errors in browser:**
   - Make sure you're accessing the frontend through a proper URL (not file://)
   - Use a local server or VS Code Live Server

3. **Port already in use:**
   - Change the port in `run.py` or stop other services using port 8000

4. **Items not updating:**
   - Check browser console for JavaScript errors
   - Refresh the page and try again

### Getting Help

If you encounter issues:
1. Check the browser console for error messages
2. Verify the backend server logs
3. Ensure all dependencies are installed correctly

## 📄 License

This project is open source and available under the MIT License.

## 🤝 Contributing

This project is designed for small Indian businesses. Contributions that improve usability, add relevant features, or enhance performance are welcome.

---

**Built with ❤️ for Indian Small Business Owners**

*Simple • Efficient • Reliable*
